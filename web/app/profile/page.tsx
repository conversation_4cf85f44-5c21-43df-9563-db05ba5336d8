'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { authGet, authDelete } from '@/lib/authFetch';
import { useAnalytics } from '@/hooks/useAnalytics';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Seo from '@/components/Seo';
import Toast from '@/components/Toast';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface Purchase {
  id: string;
  status: 'pending' | 'paid' | 'expired' | 'failed';
  created_at: string;
  invoice_id: string;
  invoice_url?: string;
  amount: number;
  currency: string;
  token_amount: number;
  token_package: 'coba' | 'hemat' | 'pro';
  payment_method: string;
  payment_completed_at?: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export default function ProfilePage() {
  const auth = useAuth();
  const { user, loading, profile, profileLoading } = auth;
  const { trackEvent } = useAnalytics();
  const router = useRouter();
  
  const [purchases, setPurchases] = useState<Purchase[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [toast, setToast] = useState({ show: false, message: '', type: 'success' as 'success' | 'error' });
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 5,
    hasNextPage: false,
    hasPreviousPage: false
  });
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);


  // Track page view
  useEffect(() => {
    trackEvent('Profile Page Viewed');
  }, []);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  // Fetch user purchases
  useEffect(() => {
    if (user) {
      fetchPurchases(1);
    }
  }, [user]);

  const fetchPurchases = async (page: number = pagination.currentPage, limit: number = pagination.limit) => {
    try {
      setIsLoading(true);
      const response = await authGet(`/api/purchases?page=${page}&limit=${limit}`);

      if (!response.ok) {
        throw new Error('Gagal mengambil data pembelian');
      }

      const data = await response.json();
      setPurchases(data.purchases || []);
      setPagination(data.pagination || {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        limit: 5,
        hasNextPage: false,
        hasPreviousPage: false
      });
    } catch (err) {
      console.error('Error fetching purchases:', err);
      setError('Gagal memuat riwayat pembelian');
    } finally {
      setIsLoading(false);
    }
  };


  const handleContinuePayment = (purchase: Purchase) => {
    if (purchase.status === 'pending' && purchase.invoice_url) {
      // Track the continue payment event
      trackEvent('Continue Payment Clicked', {
        purchase_id: purchase.id,
        token_package: purchase.token_package,
        token_amount: purchase.token_amount
      });

      // Open payment URL in new tab
      window.open(purchase.invoice_url, '_blank');
    }
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      fetchPurchases(newPage);
    }
  };

  const handlePreviousPage = () => {
    if (pagination.hasPreviousPage) {
      handlePageChange(pagination.currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (pagination.hasNextPage) {
      handlePageChange(pagination.currentPage + 1);
    }
  };

  const handleDeleteAccount = async () => {
    if (!showDeleteConfirm) {
      setShowDeleteConfirm(true);
      return;
    }

    // Double confirmation
    const confirmText = 'HAPUS AKUN SAYA';
    const userInput = prompt(
      `Untuk mengkonfirmasi penghapusan akun, ketik "${confirmText}" (tanpa tanda kutip):`
    );

    if (userInput !== confirmText) {
      setShowDeleteConfirm(false);
      setToast({
        show: true,
        message: 'Penghapusan akun dibatalkan',
        type: 'error'
      });
      return;
    }

    setIsDeleting(true);
    setShowDeleteConfirm(false);

    try {
      // Track delete account attempt
      trackEvent('Delete Account Attempt', {
        user_id: user?.id,
        email: user?.email
      });

      const response = await authDelete('/api/account/delete');
      const data = await response.json();

      if (response.ok && data.success) {
        // Track successful account deletion
        trackEvent('Account Deleted', {
          user_id: user?.id,
          email: user?.email
        });

        setToast({
          show: true,
          message: 'Akun berhasil dihapus. Anda akan dialihkan ke halaman utama.',
          type: 'success'
        });

        // Sign out and redirect after a delay
        setTimeout(async () => {
          await auth.signOut();
          router.push('/');
        }, 3000);
      } else {
        throw new Error(data.error || 'Gagal menghapus akun');
      }
    } catch (error) {
      console.error('Error deleting account:', error);

      // Track failed account deletion
      trackEvent('Delete Account Failed', {
        user_id: user?.id,
        email: user?.email,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      setToast({
        show: true,
        message: error instanceof Error ? error.message : 'Terjadi kesalahan saat menghapus akun',
        type: 'error'
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTokenPackageName = (packageId: string) => {
    switch (packageId) {
      case 'coba':
        return 'Paket Eksplorasi';
      case 'hemat':
        return 'Paket Siap Melamar';
      case 'pro':
        return 'Paket Pejuang Karir';
      default:
        return packageId;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return (
          <span className="inline-flex items-center justify-center sm:mb-0.5 px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 text-center">
            Lunas
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center justify-center sm:mb-0.5 px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 text-center">
            Menunggu Pembayaran
          </span>
        );
      case 'expired':
        return (
          <span className="inline-flex items-center justify-center sm:mb-0.5 px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 text-center">
            Kedaluwarsa
          </span>
        );
      case 'failed':
        return (
          <span className="inline-flex items-center justify-center sm:mb-0.5 px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 text-center">
            Gagal
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center justify-center sm:mb-0.5 px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 text-center">
            {status}
          </span>
        );
    }
  };

  if (loading) {
    return (
      <main className="min-h-screen flex flex-col pt-16">
        <Navbar auth={auth} />
        <div className="flex-grow flex items-center justify-center">
          <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <Footer />
      </main>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <main className="min-h-screen flex flex-col pt-16">
      <Toast 
        show={toast.show} 
        message={toast.message} 
        type={toast.type} 
        onClose={() => setToast({ ...toast, show: false })} 
      />
      <Seo 
        title="Profil Pengguna - Gigsta"
        description="Kelola profil dan riwayat pembelian surat lamaran Anda di Gigsta"
        canonical="https://gigsta.io/profile"
      />
      <Navbar auth={auth} />
      
      <section className="pt-6 sm:pt-8 pb-12 flex-grow">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* User Info Section */}
          <div className="bg-white shadow-md rounded-lg p-6 mb-8">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-xl font-semibold">Informasi Akun</h2>
              <button
                onClick={handleDeleteAccount}
                disabled={isDeleting}
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  showDeleteConfirm
                    ? 'text-white bg-red-600 hover:bg-red-700 focus:ring-red-500'
                    : 'text-red-600 bg-red-50 hover:bg-red-100 focus:ring-red-500'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                  isDeleting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isDeleting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Menghapus...
                  </>
                ) : showDeleteConfirm ? (
                  <>
                    <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    Konfirmasi Hapus
                  </>
                ) : (
                  <>
                    <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Hapus Akun
                  </>
                )}
              </button>
            </div>

            {showDeleteConfirm && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Peringatan: Tindakan ini tidak dapat dibatalkan
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>
                        Menghapus akun akan menghapus semua data Anda secara permanen, termasuk:
                      </p>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>Profil dan informasi akun</li>
                        <li>Riwayat pembelian token</li>
                        <li>Surat lamaran dan CV yang tersimpan</li>
                        <li>Email aplikasi yang tersimpan</li>
                        <li>File resume yang diunggah</li>
                      </ul>
                      <p className="mt-2 font-medium">
                        Klik tombol "Konfirmasi Hapus" di atas untuk melanjutkan.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 leading-none">{user.user_metadata?.full_name || 'Pengguna'}</h3>
                <p className="text-gray-600 mb-1">{user.email}</p>
                <p className="text-sm text-gray-500">
                  Bergabung pada {formatDate(user.created_at)}
                </p>
              </div>
            </div>
          </div>

          {/* Token Information Section */}
          <div className="bg-white shadow-md rounded-lg p-4 sm:p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Informasi Token</h2>
            <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-lg p-4 sm:p-6">
              {/* Token Balance - Mobile Optimized */}
              <div className="text-center">
                <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-amber-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M21 6.375C21 9.06739 16.9706 11.25 12 11.25C7.02944 11.25 3 9.06739 3 6.375C3 3.68261 7.02944 1.5 12 1.5C16.9706 1.5 21 3.68261 21 6.375Z" />
                    <path d="M12 12.75C14.6852 12.75 17.1905 12.1637 19.0784 11.1411C19.7684 10.7673 20.4248 10.3043 20.9747 9.75674C20.9915 9.87831 21 10.0011 21 10.125C21 12.8174 16.9706 15 12 15C7.02944 15 3 12.8174 3 10.125C3 10.0011 3.00853 9.8783 3.02529 9.75674C3.57523 10.3043 4.23162 10.7673 4.92161 11.1411C6.80949 12.1637 9.31481 12.75 12 12.75Z" />
                    <path d="M12 16.5C14.6852 16.5 17.1905 15.9137 19.0784 14.8911C19.7684 14.5173 20.4248 14.0543 20.9747 13.5067C20.9915 13.6283 21 13.7511 21 13.875C21 16.5674 16.9706 18.75 12 18.75C7.02944 18.75 3 16.5674 3 13.875C3 13.7511 3.00853 13.6283 3.02529 13.5067C3.57523 14.0543 4.23162 14.5173 4.92161 14.8911C6.80949 15.9137 9.31481 16.5 12 16.5Z" />
                    <path d="M12 20.25C14.6852 20.25 17.1905 19.6637 19.0784 18.6411C19.7684 18.2673 20.4248 17.8043 20.9747 17.2567C20.9915 17.3783 21 17.5011 21 17.625C21 20.3174 16.9706 22.5 12 22.5C7.02944 22.5 3 20.3174 3 17.625C3 17.5011 3.00853 17.3783 3.02529 17.2567C3.57523 17.8043 4.23162 18.2673 4.92161 18.6411C6.80949 19.6637 9.31481 20.25 12 20.25Z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-amber-800 mb-2">Saldo Token</h3>
                <p className="text-3xl font-bold text-amber-900 mb-2">
                  {profileLoading ? (
                    <span className="inline-flex items-center">
                      <svg className="animate-spin h-6 w-6 text-amber-600 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Memuat...
                    </span>
                  ) : (
                    `${profile?.tokens ?? 0} Token`
                  )}
                </p>
                <p className="text-sm text-amber-700 mb-4">
                  Token digunakan untuk mengakses fitur premium
                </p>

                {/* Buy Tokens Button */}
                <Link
                  href="/buy-tokens"
                  className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Beli Token
                </Link>
              </div>

              {/* Token Usage Guide */}
              <div className="mt-6 pt-6 border-t border-amber-200">
                <div className="mb-4">
                  <h4 className="text-md font-semibold text-amber-800 mb-2">Panduan Penggunaan Token</h4>
                  <p className="text-sm text-amber-700">Berikut adalah biaya token untuk setiap fitur premium:</p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                  <div className="text-center bg-white/50 rounded-lg p-3 border border-amber-100">
                    <div className="text-lg font-semibold text-amber-800">5 Token</div>
                    <div className="text-sm text-amber-600">Email Lamaran</div>
                  </div>
                  <div className="text-center bg-white/50 rounded-lg p-3 border border-amber-100">
                    <div className="text-lg font-semibold text-amber-800">10 Token</div>
                    <div className="text-sm text-amber-600">Surat Lamaran Standar</div>
                  </div>
                  <div className="text-center bg-white/50 rounded-lg p-3 border border-amber-100">
                    <div className="text-lg font-semibold text-amber-800">15 Token</div>
                    <div className="text-sm text-amber-600">Surat Lamaran Premium</div>
                  </div>
                  <div className="text-center bg-white/50 rounded-lg p-3 border border-amber-100">
                    <div className="text-lg font-semibold text-amber-800">15 Token</div>
                    <div className="text-sm text-amber-600">CV Manual</div>
                  </div>
                  <div className="text-center bg-white/50 rounded-lg p-3 border border-amber-100">
                    <div className="text-lg font-semibold text-amber-800">20 Token</div>
                    <div className="text-sm text-amber-600">CV Spesifik Pekerjaan</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Purchase History Section */}
          <div className="bg-white shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Riwayat Pembelian</h2>
            
            {isLoading ? (
              <div className="flex justify-center py-8">
                <svg className="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Gagal memuat data</h3>
                <p className="mt-1 text-sm text-gray-500">{error}</p>
                <button
                  onClick={() => fetchPurchases(1)}
                  className="mt-4 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                >
                  <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Coba Lagi
                </button>
              </div>
            ) : purchases.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada pembelian</h3>
                <p className="mt-1 text-sm text-gray-500">Anda belum melakukan pembelian token</p>
                <Link
                  href="/buy-tokens"
                  className="mt-4 inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-white bg-primary hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                >
                  <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Beli Token
                </Link>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  {purchases.map((purchase) => (
                    <div key={purchase.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-3">
                            <h3 className="text-lg font-medium leading-tight text-gray-900">
                              {getTokenPackageName(purchase.token_package)} - {purchase.token_amount} Token
                            </h3>
                            <div className="flex-shrink-0">
                              {getStatusBadge(purchase.status)}
                            </div>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">
                            Dibeli pada {formatDate(purchase.created_at)} • {formatCurrency(purchase.amount)}
                          </p>
                          <p className="text-xs text-gray-400">
                            ID Pembelian: {purchase.id}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2 flex-wrap gap-2 flex-shrink-0">
                          {purchase.status === 'pending' && (
                            <button
                              onClick={() => handleContinuePayment(purchase)}
                              className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
                            >
                              <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                              </svg>
                              Lanjutkan Pembayaran
                            </button>
                          )}
                          {purchase.status === 'paid' && (
                            <div className="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md text-green-700 bg-green-100 border border-green-200">
                              <svg className="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                              </svg>
                              Token Berhasil Ditambahkan
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                  <div className="mt-6 flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                    <div className="flex flex-1 justify-between sm:hidden">
                      <button
                        onClick={handlePreviousPage}
                        disabled={!pagination.hasPreviousPage}
                        className={`relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium ${
                          pagination.hasPreviousPage
                            ? 'text-gray-700 hover:bg-gray-50'
                            : 'text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        Sebelumnya
                      </button>
                      <button
                        onClick={handleNextPage}
                        disabled={!pagination.hasNextPage}
                        className={`relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium ${
                          pagination.hasNextPage
                            ? 'text-gray-700 hover:bg-gray-50'
                            : 'text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        Selanjutnya
                      </button>
                    </div>
                    <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          Menampilkan{' '}
                          <span className="font-medium">
                            {(pagination.currentPage - 1) * pagination.limit + 1}
                          </span>{' '}
                          sampai{' '}
                          <span className="font-medium">
                            {Math.min(pagination.currentPage * pagination.limit, pagination.totalCount)}
                          </span>{' '}
                          dari{' '}
                          <span className="font-medium">{pagination.totalCount}</span> hasil
                        </p>
                      </div>
                      <div>
                        <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                          <button
                            onClick={handlePreviousPage}
                            disabled={!pagination.hasPreviousPage}
                            className={`relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 ${
                              pagination.hasPreviousPage
                                ? 'hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                                : 'cursor-not-allowed'
                            }`}
                          >
                            <span className="sr-only">Halaman sebelumnya</span>
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                              <path fillRule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clipRule="evenodd" />
                            </svg>
                          </button>

                          {/* Page numbers */}
                          {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((pageNum) => {
                            const isCurrentPage = pageNum === pagination.currentPage;
                            const shouldShow =
                              pageNum === 1 ||
                              pageNum === pagination.totalPages ||
                              (pageNum >= pagination.currentPage - 1 && pageNum <= pagination.currentPage + 1);

                            if (!shouldShow) {
                              if (pageNum === pagination.currentPage - 2 || pageNum === pagination.currentPage + 2) {
                                return (
                                  <span key={pageNum} className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">
                                    ...
                                  </span>
                                );
                              }
                              return null;
                            }

                            return (
                              <button
                                key={pageNum}
                                onClick={() => handlePageChange(pageNum)}
                                className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                                  isCurrentPage
                                    ? 'z-10 bg-primary text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary'
                                    : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                                }`}
                              >
                                {pageNum}
                              </button>
                            );
                          })}

                          <button
                            onClick={handleNextPage}
                            disabled={!pagination.hasNextPage}
                            className={`relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 ${
                              pagination.hasNextPage
                                ? 'hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                                : 'cursor-not-allowed'
                            }`}
                          >
                            <span className="sr-only">Halaman selanjutnya</span>
                            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                              <path fillRule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clipRule="evenodd" />
                            </svg>
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Report Problem Button */}
        <div className="text-center mt-12">
          <Link href="/report-problem" className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            Laporkan Masalah
          </Link>
        </div>
      </section>

      <Footer />
    </main>
  );
}

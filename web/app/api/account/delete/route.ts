export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { createApi<PERSON>andler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { trackApiUsage, trackEvent } from '@/lib/mixpanel-server';

async function handleRequest(request: NextRequest) {
  // Start timing the request
  const startTime = Date.now();
  let userId: string | undefined;
  
  try {
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');
    
    if (!accessToken) {
      return NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
    }
    
    // Create Supabase client
    const supabase = await createClient();
    
    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);
    
    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({
        success: false,
        error: 'Authentication required'
      }, { status: 401 });
    }

    userId = user.id;
    const userEmail = user.email;

    console.log(`Starting account deletion process for user: ${userId}`);

    // Start a transaction-like cleanup process
    const cleanupErrors: string[] = [];

    try {
      // 1. Delete user's resume files from storage
      const { data: profile } = await supabase
        .from('profiles')
        .select('resume_file_name')
        .eq('id', userId)
        .single();

      if (profile?.resume_file_name) {
        const { error: storageError } = await supabase
          .storage
          .from('resumes')
          .remove([profile.resume_file_name]);
        
        if (storageError) {
          console.error('Error deleting resume file:', storageError);
          cleanupErrors.push('Failed to delete resume file');
        }
      }

      // 2. Delete user data from database tables (in order of dependencies)
      const tablesToCleanup = [
        'user_feedback',
        'emails', 
        'letters',
        'resumes',
        'purchases',
        'profiles'
      ];

      for (const table of tablesToCleanup) {
        const { error: deleteError } = await supabase
          .from(table)
          .delete()
          .eq('user_id', userId);
        
        if (deleteError) {
          console.error(`Error deleting from ${table}:`, deleteError);
          cleanupErrors.push(`Failed to delete ${table} data`);
        } else {
          console.log(`Successfully deleted user data from ${table}`);
        }
      }

      // 3. Delete the user from Supabase Auth
      // Note: This requires admin privileges, so we'll use the service role
      const { error: authDeleteError } = await supabase.auth.admin.deleteUser(userId);
      
      if (authDeleteError) {
        console.error('Error deleting user from auth:', authDeleteError);
        cleanupErrors.push('Failed to delete user authentication');
      }

      // Calculate request duration
      const duration = Date.now() - startTime;

      if (cleanupErrors.length > 0) {
        // Partial failure - log the issues but still consider it a partial success
        console.warn('Account deletion completed with some errors:', cleanupErrors);
        
        // Track partial success
        trackApiUsage(
          'delete-account', 
          'partial-success',
          duration,
          {
            user_id: userId,
            email: userEmail,
            cleanup_errors: cleanupErrors
          },
          userId
        );

        trackEvent(
          'Account Deletion Partial Success',
          {
            cleanup_errors: cleanupErrors,
            user_id: userId,
            email: userEmail
          },
          userId
        );

        return NextResponse.json({
          success: true,
          message: 'Akun berhasil dihapus dengan beberapa peringatan',
          warnings: cleanupErrors
        });
      } else {
        // Complete success
        console.log(`Account deletion completed successfully for user: ${userId}`);
        
        // Track successful account deletion
        trackApiUsage(
          'delete-account', 
          'success',
          duration,
          {
            user_id: userId,
            email: userEmail
          },
          userId
        );

        trackEvent(
          'Account Deleted Successfully',
          {
            user_id: userId,
            email: userEmail
          },
          userId
        );

        return NextResponse.json({
          success: true,
          message: 'Akun berhasil dihapus sepenuhnya'
        });
      }

    } catch (cleanupError) {
      console.error('Error during account cleanup:', cleanupError);
      throw cleanupError;
    }
    
  } catch (error) {
    console.error('Error deleting account:', error);
    
    // Calculate request duration even for errors
    const duration = Date.now() - startTime;
    
    // Track failed account deletion
    trackApiUsage(
      'delete-account', 
      'error',
      duration,
      {
        error_message: error instanceof Error ? error.message : 'Unknown error',
        user_id: userId
      },
      userId
    );
    
    trackEvent(
      'Account Deletion Failed',
      {
        error: error instanceof Error ? error.message : 'Unknown error',
        user_id: userId
      },
      userId
    );
    
    // Capture error details with monitoring
    captureApiError('delete-account', error, {
      request_url: request.url,
      user_id: userId
    });
    
    return NextResponse.json({
      success: false,
      error: 'Gagal menghapus akun. Silakan coba lagi atau hubungi dukungan.'
    }, { status: 500 });
  }
}

export const DELETE = createApiHandler('delete-account', handleRequest);

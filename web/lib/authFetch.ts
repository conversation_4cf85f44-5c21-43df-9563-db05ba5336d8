/**
 * Utility function for making API requests
 * Authentication is now handled automatically via cookies by Supabase SSR
 * These functions are kept for backward compatibility but now just wrap regular fetch
 */

/**
 * Makes a fetch request to an API endpoint
 * Authentication is handled automatically via cookies
 *
 * @param url The API endpoint URL
 * @param options Additional fetch options
 * @returns The fetch response
 */
export async function authFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Authentication is now handled automatically via cookies
  return fetch(url, options);
}

/**
 * Makes a GET request
 * Authentication is handled automatically via cookies
 */
export async function authGet(url: string, options: RequestInit = {}): Promise<Response> {
  return fetch(url, {
    ...options,
    method: 'GET'
  });
}

/**
 * Makes a POST request with FormData
 * Authentication is handled automatically via cookies
 */
export async function authPost(url: string, body: Record<string, any>, options: RequestInit = {}): Promise<Response> {
  const formData = new FormData();

  // Append all body fields to FormData
  Object.entries(body).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      formData.append(key, value);
    }
  });

  // Don't set Content-Type header, let the browser set it with the correct boundary
  const { headers: _, ...restOptions } = options;

  return fetch(url, {
    ...restOptions,
    method: 'POST',
    body: formData
  });
}

/**
 * Makes a DELETE request with JSON body
 * Authentication is handled automatically via cookies
 */
export async function authDelete(url: string, body: any = {}, options: RequestInit = {}): Promise<Response> {
  const headers = new Headers(options.headers || {});
  headers.set('Content-Type', 'application/json');

  return fetch(url, {
    ...options,
    method: 'DELETE',
    body: JSON.stringify(body),
    headers
  });
}

/**
 * Makes a POST request with FormData body
 * Authentication is handled automatically via cookies
 */
export async function authPostFormData(url: string, formData: FormData, options: RequestInit = {}): Promise<Response> {
  return fetch(url, {
    ...options,
    method: 'POST',
    body: formData
  });
}
